<Window x:Class="Zview.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:Zview"
        mc:Ignorable="d"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        Height="1000"
        Width="1200"
        KeyDown="Window_KeyDown"
        Focusable="True"
        WindowStartupLocation="CenterScreen">
    <Border Background="#1b1b1b"
            BorderBrush="#333333"
            BorderThickness="1"
            CornerRadius="10"
            AllowDrop="True"
            Drop="Border_Drop"
            DragEnter="Border_DragEnter"
            DragOver="Border_DragOver">
        <Grid AllowDrop="True"
              Drop="Grid_Drop"
              DragEnter="Grid_DragEnter"
              DragOver="Grid_DragOver"
              MouseLeftButtonDown="Grid_MouseLeftButtonDown"
              Background="Transparent">
            <Image x:Name="ImageViewer"
                   Stretch="Uniform"
                   StretchDirection="Both"
                   HorizontalAlignment="Center"
                   VerticalAlignment="Center"/>
            <TextBlock x:Name="DropHintText"
                       Text="Drop an image here or use mouse wheel to navigate"
                       HorizontalAlignment="Center"
                       VerticalAlignment="Center"
                       Foreground="#666666"
                       FontSize="20"
                       FontWeight="Light"
                       TextAlignment="Center"/>
            <Button x:Name="TestButton"
                    Content="Test Click"
                    Click="TestButton_Click"
                    Padding="20,10"
                    Background="#333333"
                    Foreground="White"
                    BorderBrush="#555555"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Bottom"
                    Margin="0,0,0,50"/>
        </Grid>
    </Border>
</Window>
