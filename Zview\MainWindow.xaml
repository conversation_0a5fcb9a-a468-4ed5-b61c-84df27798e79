<Window x:Class="Zview.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:Zview"
        mc:Ignorable="d"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        Height="1000"
        Width="1200"
        KeyDown="Window_KeyDown">
    <Border Background="#1b1b1b"
            BorderBrush="#333333"
            BorderThickness="1"
            CornerRadius="10">
        <Grid AllowDrop="True"
              Drop="Grid_Drop"
              DragEnter="Grid_DragEnter"
              DragOver="Grid_DragOver">
            <Image x:Name="ImageViewer"
                   Stretch="Uniform"
                   StretchDirection="Both"
                   HorizontalAlignment="Center"
                   VerticalAlignment="Center"/>
            <TextBlock x:Name="DropHintText"
                       Text="Drop an image here"
                       HorizontalAlignment="Center"
                       VerticalAlignment="Center"
                       Foreground="#666666"
                       FontSize="24"
                       FontWeight="Light"/>
        </Grid>
    </Border>
</Window>
