{"runtimeOptions": {"tfm": "net10.0", "frameworks": [{"name": "Microsoft.NETCore.App", "version": "10.0.0-preview.5.25277.114"}, {"name": "Microsoft.WindowsDesktop.App", "version": "10.0.0-preview.5.25277.114"}], "additionalProbingPaths": ["C:\\Users\\<USER>\\.dotnet\\store\\|arch|\\|tfm|", "C:\\Users\\<USER>\\.nuget\\packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configProperties": {"System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization": false, "CSWINRT_USE_WINDOWS_UI_XAML_PROJECTIONS": false, "Microsoft.NETCore.DotNetHostPolicy.SetAppPaths": true}}}