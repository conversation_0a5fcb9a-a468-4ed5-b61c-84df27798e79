using System;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media.Imaging;

namespace Zview
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        private readonly string[] supportedImageExtensions = { ".jpg", ".jpeg", ".png", ".bmp", ".gif", ".tiff", ".webp" };

        public MainWindow()
        {
            InitializeComponent();
        }

        private void Grid_DragEnter(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent(DataFormats.FileDrop))
            {
                string[] files = (string[])e.Data.GetData(DataFormats.FileDrop);
                if (files.Length > 0 && IsImageFile(files[0]))
                {
                    e.Effects = DragDropEffects.Copy;
                }
                else
                {
                    e.Effects = DragDropEffects.None;
                }
            }
            else
            {
                e.Effects = DragDropEffects.None;
            }
        }

        private void Grid_DragOver(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent(DataFormats.FileDrop))
            {
                string[] files = (string[])e.Data.GetData(DataFormats.FileDrop);
                if (files.Length > 0 && IsImageFile(files[0]))
                {
                    e.Effects = DragDropEffects.Copy;
                }
                else
                {
                    e.Effects = DragDropEffects.None;
                }
            }
            else
            {
                e.Effects = DragDropEffects.None;
            }
            e.Handled = true;
        }

        private void Grid_Drop(object sender, DragEventArgs e)
        {
            try
            {
                Console.WriteLine("Drop event triggered");
                if (e.Data.GetDataPresent(DataFormats.FileDrop))
                {
                    string[] files = (string[])e.Data.GetData(DataFormats.FileDrop);
                    Console.WriteLine($"Files dropped: {files.Length}");
                    if (files.Length > 0)
                    {
                        Console.WriteLine($"First file: {files[0]}");
                        if (IsImageFile(files[0]))
                        {
                            Console.WriteLine("Loading image...");
                            LoadImage(files[0]);
                        }
                        else
                        {
                            Console.WriteLine("File is not a supported image format");
                        }
                    }
                }
                else
                {
                    Console.WriteLine("No file drop data present");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in Drop event: {ex.Message}");
                MessageBox.Show($"Error in drop operation: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool IsImageFile(string filePath)
        {
            string extension = Path.GetExtension(filePath).ToLower();
            return supportedImageExtensions.Contains(extension);
        }

        private void LoadImage(string imagePath)
        {
            try
            {
                Console.WriteLine($"LoadImage called with: {imagePath}");
                BitmapImage bitmap = new BitmapImage();
                bitmap.BeginInit();
                bitmap.UriSource = new Uri(imagePath);
                bitmap.CacheOption = BitmapCacheOption.OnLoad;
                bitmap.EndInit();

                Console.WriteLine("Image loaded successfully, setting to ImageViewer");
                ImageViewer.Source = bitmap;
                DropHintText.Visibility = Visibility.Collapsed;
                Console.WriteLine("Image display completed");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading image: {ex.Message}");
                MessageBox.Show($"Error loading image: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Window_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Escape)
            {
                this.Close();
            }
        }

        private void Grid_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            Console.WriteLine("Grid clicked - events are working");
            // Allow window dragging
            this.DragMove();
        }

        // Border drag and drop handlers (backup in case Grid handlers don't work)
        private void Border_DragEnter(object sender, DragEventArgs e)
        {
            Console.WriteLine("Border_DragEnter triggered");
            Grid_DragEnter(sender, e);
        }

        private void Border_DragOver(object sender, DragEventArgs e)
        {
            Console.WriteLine("Border_DragOver triggered");
            Grid_DragOver(sender, e);
        }

        private void Border_Drop(object sender, DragEventArgs e)
        {
            Console.WriteLine("Border_Drop triggered");
            Grid_Drop(sender, e);
        }
    }
}