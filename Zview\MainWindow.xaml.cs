using System;
using System.Buffers;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media.Imaging;

namespace Zview
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        // Comprehensive list of supported image formats - using HashSet for O(1) lookup
        private static readonly HashSet<string> SupportedImageExtensions = new(StringComparer.OrdinalIgnoreCase)
        {
            ".jpg", ".jpeg", ".png", ".bmp", ".gif", ".tiff", ".tif", ".webp",
            ".ico", ".wdp", ".hdp", ".jxr", ".dds", ".heic", ".heif", ".avif",
            ".svg", ".raw", ".cr2", ".nef", ".arw", ".dng", ".orf", ".rw2",
            ".pef", ".srw", ".x3f", ".raf", ".3fr", ".fff", ".dcr", ".kdc",
            ".srf", ".mrw", ".nrw", ".rwl", ".iiq", ".3fr", ".ari", ".bay",
            ".crw", ".erf", ".mef", ".mos", ".ptx", ".pxn", ".r3d"
        };

        private string[] currentImageList = Array.Empty<string>(); // Use array for better performance
        private int currentImageIndex = -1;
        private string currentDirectory = string.Empty; // Use string.Empty instead of ""

        public MainWindow()
        {
            InitializeComponent();

            // Enable mouse wheel events
            this.MouseWheel += MainWindow_MouseWheel;
        }

        private void Grid_DragEnter(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent(DataFormats.FileDrop))
            {
                string[] files = (string[])e.Data.GetData(DataFormats.FileDrop);
                if (files.Length > 0 && IsImageFile(files[0]))
                {
                    e.Effects = DragDropEffects.Copy;
                }
                else
                {
                    e.Effects = DragDropEffects.None;
                }
            }
            else
            {
                e.Effects = DragDropEffects.None;
            }
        }

        private void Grid_DragOver(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent(DataFormats.FileDrop))
            {
                string[] files = (string[])e.Data.GetData(DataFormats.FileDrop);
                if (files.Length > 0 && IsImageFile(files[0]))
                {
                    e.Effects = DragDropEffects.Copy;
                }
                else
                {
                    e.Effects = DragDropEffects.None;
                }
            }
            else
            {
                e.Effects = DragDropEffects.None;
            }
            e.Handled = true;
        }

        private void Grid_Drop(object sender, DragEventArgs e)
        {
            try
            {
                Console.WriteLine("Drop event triggered");
                if (e.Data.GetDataPresent(DataFormats.FileDrop))
                {
                    string[] files = (string[])e.Data.GetData(DataFormats.FileDrop);
                    Console.WriteLine($"Files dropped: {files.Length}");
                    if (files.Length > 0)
                    {
                        Console.WriteLine($"First file: {files[0]}");
                        if (IsImageFile(files[0]))
                        {
                            Console.WriteLine("Loading image...");
                            LoadImage(files[0]);
                        }
                        else
                        {
                            Console.WriteLine("File is not a supported image format");
                        }
                    }
                }
                else
                {
                    Console.WriteLine("No file drop data present");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in Drop event: {ex.Message}");
                MessageBox.Show($"Error in drop operation: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private static bool IsImageFile(ReadOnlySpan<char> filePath)
        {
            // Use ReadOnlySpan for zero-allocation path processing
            var extensionSpan = Path.GetExtension(filePath);
            return SupportedImageExtensions.Contains(extensionSpan.ToString());
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private static bool IsImageFile(string filePath)
        {
            return IsImageFile(filePath.AsSpan());
        }

        private void LoadImage(string imagePath)
        {
            try
            {
                Console.WriteLine($"LoadImage called with: {imagePath}");

                // Update the image list when a new image is loaded
                UpdateImageList(imagePath);

                BitmapImage bitmap = new BitmapImage();
                bitmap.BeginInit();
                bitmap.UriSource = new Uri(imagePath);
                bitmap.CacheOption = BitmapCacheOption.OnLoad;
                bitmap.EndInit();

                Console.WriteLine("Image loaded successfully, setting to ImageViewer");
                ImageViewer.Source = bitmap;
                DropHintText.Visibility = Visibility.Collapsed;
                Console.WriteLine("Image display completed");

                // Update window title to show current image info
                UpdateWindowTitle();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading image: {ex.Message}");
                MessageBox.Show($"Error loading image: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateImageList(string imagePath)
        {
            try
            {
                var directory = Path.GetDirectoryName(imagePath);
                if (!string.Equals(directory, currentDirectory, StringComparison.OrdinalIgnoreCase))
                {
                    currentDirectory = directory ?? string.Empty;

                    // Use more efficient file enumeration and filtering
                    var imageFiles = Directory.EnumerateFiles(currentDirectory)
                        .Where(IsImageFile)
                        .Order(StringComparer.OrdinalIgnoreCase)
                        .ToArray(); // Use array for better performance

                    currentImageList = imageFiles;
                    Console.WriteLine($"Found {currentImageList.Length} images in directory");
                }

                // Use Array.FindIndex for better performance than List.FindIndex
                currentImageIndex = Array.FindIndex(currentImageList, img =>
                    string.Equals(img, imagePath, StringComparison.OrdinalIgnoreCase));

                Console.WriteLine($"Current image index: {currentImageIndex + 1}/{currentImageList.Length}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating image list: {ex.Message}");
            }
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private void UpdateWindowTitle()
        {
            if (currentImageIndex >= 0 && currentImageList.Length > 0)
            {
                var fileName = Path.GetFileName(currentImageList[currentImageIndex].AsSpan());
                this.Title = $"Zview - {fileName} ({currentImageIndex + 1}/{currentImageList.Length})";
            }
            else
            {
                this.Title = "Zview";
            }
        }

        private void Window_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.Key)
            {
                case Key.Escape:
                    this.Close();
                    break;
                case Key.Right:
                case Key.Down:
                case Key.Space:
                    ShowNextImage();
                    break;
                case Key.Left:
                case Key.Up:
                    ShowPreviousImage();
                    break;
                case Key.Home:
                    if (currentImageList.Length > 0)
                    {
                        currentImageIndex = 0;
                        LoadImageByIndex();
                    }
                    break;
                case Key.End:
                    if (currentImageList.Length > 0)
                    {
                        currentImageIndex = currentImageList.Length - 1;
                        LoadImageByIndex();
                    }
                    break;
            }
        }

        private void Grid_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            Console.WriteLine("Grid clicked - events are working");
            // Allow window dragging
            this.DragMove();
        }

        private void Grid_MouseDown(object sender, MouseButtonEventArgs e)
        {
            if (e.MiddleButton == MouseButtonState.Pressed)
            {
                Console.WriteLine("Middle mouse button clicked - closing application");
                this.Close();
            }
        }

        // Border drag and drop handlers (backup in case Grid handlers don't work)
        private void Border_DragEnter(object sender, DragEventArgs e)
        {
            Console.WriteLine("Border_DragEnter triggered");
            Grid_DragEnter(sender, e);
        }

        private void Border_DragOver(object sender, DragEventArgs e)
        {
            Console.WriteLine("Border_DragOver triggered");
            Grid_DragOver(sender, e);
        }

        private void Border_Drop(object sender, DragEventArgs e)
        {
            Console.WriteLine("Border_Drop triggered");
            Grid_Drop(sender, e);
        }



        private void MainWindow_MouseWheel(object sender, MouseWheelEventArgs e)
        {
            if (currentImageList.Length <= 1) return;

            if (e.Delta > 0)
            {
                // Scroll up - go to previous image
                ShowPreviousImage();
            }
            else
            {
                // Scroll down - go to next image
                ShowNextImage();
            }
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private void ShowNextImage()
        {
            if (currentImageList.Length == 0) return;

            currentImageIndex = (currentImageIndex + 1) % currentImageList.Length;
            LoadImageByIndex();
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private void ShowPreviousImage()
        {
            if (currentImageList.Length == 0) return;

            currentImageIndex = (currentImageIndex - 1 + currentImageList.Length) % currentImageList.Length;
            LoadImageByIndex();
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private void LoadImageByIndex()
        {
            if (currentImageIndex >= 0 && currentImageIndex < currentImageList.Length)
            {
                string imagePath = currentImageList[currentImageIndex];
                try
                {
                    Console.WriteLine($"Loading image by index: {currentImageIndex + 1}/{currentImageList.Length}");

                    var bitmap = new BitmapImage();
                    bitmap.BeginInit();
                    bitmap.UriSource = new Uri(imagePath);
                    bitmap.CacheOption = BitmapCacheOption.OnLoad;
                    bitmap.EndInit();

                    ImageViewer.Source = bitmap;
                    UpdateWindowTitle();

                    Console.WriteLine($"Loaded: {Path.GetFileName(imagePath.AsSpan())}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error loading image by index: {ex.Message}");
                    // Skip to next image if current one fails to load
                    ShowNextImage();
                }
            }
        }
    }
}