﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net10.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
    <Platforms>AnyCPU;x64</Platforms>

    <!-- Performance optimizations -->
    <PublishAot>false</PublishAot> <!-- WPF doesn't support AOT yet -->
    <TieredCompilation>true</TieredCompilation>
    <TieredPGO>true</TieredPGO>
    <ReadyToRun>true</ReadyToRun>
    <PublishReadyToRun>true</PublishReadyToRun>
    <DebugType>none</DebugType>
    <DebugSymbols>false</DebugSymbols>
    <Optimize>true</Optimize>
    <!-- Trimming disabled for WPF compatibility -->
    <PublishTrimmed>false</PublishTrimmed>

    <!-- Additional performance settings -->
    <ServerGarbageCollection>false</ServerGarbageCollection>
    <ConcurrentGarbageCollection>true</ConcurrentGarbageCollection>
    <RetainVMGarbageCollection>false</RetainVMGarbageCollection>
  </PropertyGroup>

</Project>
